package com.yxt.dynamic.routing.proxy;

import com.alibaba.cloud.nacos.discovery.NacosDiscoveryClient;
import com.alibaba.fastjson.JSONObject;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.Random;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.util.CollectionUtils;

/**
 * @Description: Feign请求拦截器，实现动态路由转发功能
 * <AUTHOR>
 * @Date 2024/9/9 17:30
 */
@Slf4j
public class DynamicFeignRequestInterceptor implements RequestInterceptor {

  private DynamicProperties dynamicProperties;
  private NacosDiscoveryClient nacosDiscoveryClient;
  private Random random = new Random();

  public DynamicFeignRequestInterceptor(DynamicProperties dynamicProperties,
      NacosDiscoveryClient nacosDiscoveryClient) {
    this.dynamicProperties = dynamicProperties;
    this.nacosDiscoveryClient = nacosDiscoveryClient;
  }


  @Override
  public void apply(RequestTemplate template) {
    String allRoute = DynamicRouteContext.getAllRoute();
    if (StringUtils.isBlank(allRoute)) {
      return;
    }
    template.header(DynamicRouteContext.ROUTE_KEY, allRoute);

    try {
      // 解析路由配置
      JSONObject routeMap = JSONObject.parseObject(allRoute);

      // 获取当前请求的目标URL
      String currentUrl = template.url();
      String currentTarget = template.feignTarget().url();

      log.debug("当前请求URL: {}, 目标服务: {}", currentUrl, currentTarget);

      // 遍历路由配置，寻找匹配的路由规则
      for (Map.Entry<String, Object> entry : routeMap.entrySet()) {
        String sourceKey = entry.getKey();
        String targetValue = String.valueOf(entry.getValue());

        if (shouldApplyRoute(currentTarget, sourceKey)) {
          String selectedTarget = selectTargetInstance(targetValue);
          if (StringUtils.isNotBlank(selectedTarget)) {
            applyRouting(template, sourceKey, selectedTarget);
            log.info("路由转发: {} -> {}", sourceKey, selectedTarget);
            break;
          }
        }
      }

    } catch (Exception e) {
      log.error("动态路由转发处理失败: {}", e.getMessage(), e);
    }
  }

  /**
   * 判断是否应该应用路由规则
   */
  private boolean shouldApplyRoute(String currentTarget, String sourceKey) {
    // 如果是IP地址格式
    if (sourceKey.startsWith("http://") || sourceKey.startsWith("https://")) {
      return currentTarget.equals(sourceKey);
    }
    // 如果是服务名称
    else {
      // 检查当前目标是否包含该服务名称
      return currentTarget.contains(sourceKey) ||
          (nacosDiscoveryClient != null && isServiceNameMatch(currentTarget, sourceKey));
    }
  }

  /**
   * 检查服务名称是否匹配
   */
  private boolean isServiceNameMatch(String currentTarget, String serviceName) {
    try {
      // 从Nacos获取服务实例来验证服务名称
      List<ServiceInstance> instances = nacosDiscoveryClient.getInstances(serviceName);
      if (!CollectionUtils.isEmpty(instances)) {
        // 检查当前目标是否指向该服务的实例
        for (ServiceInstance instance : instances) {
          String instanceUrl = instance.getUri().toString();
          if (currentTarget.equals(instanceUrl)) {
            return true;
          }
        }
      }
    } catch (Exception e) {
      log.warn("检查服务名称匹配时发生异常: {}", e.getMessage());
    }
    return false;
  }

  /**
   * 从目标配置中选择一个实例（支持多实例用逗号分隔）
   */
  private String selectTargetInstance(String targetValue) {
    if (StringUtils.isBlank(targetValue)) {
      return null;
    }

    // 支持多个目标实例，用逗号分隔
    String[] targets = targetValue.split(",");
    if (targets.length == 1) {
      return targets[0].trim();
    }

    // 随机选择一个实例
    int index = random.nextInt(targets.length);
    return targets[index].trim();
  }

  /**
   * 应用路由转发
   */
  private void applyRouting(RequestTemplate template, String sourceKey, String targetValue) {
    try {
      // 如果是IP地址格式的直接替换
      if (sourceKey.startsWith("http://") || sourceKey.startsWith("https://")) {
        replaceDirectUrl(template, sourceKey, targetValue);
      }
      // 如果是服务名称，需要处理服务发现
      else {
        replaceServiceName(template, sourceKey, targetValue);
      }
    } catch (Exception e) {
      log.error("应用路由转发失败: source={}, target={}, error={}", sourceKey, targetValue,
          e.getMessage(), e);
    }
  }

  /**
   * 替换直接URL
   */
  private void replaceDirectUrl(RequestTemplate template, String sourceUrl, String targetUrl) {
    String currentUrl = template.url();
    String newUrl = currentUrl.replace(sourceUrl, targetUrl);
    template.target(targetUrl);

    // 如果URL中包含路径，需要更新
    if (!currentUrl.equals(newUrl)) {
      URI sourceUri = URI.create(sourceUrl);
      URI targetUri = URI.create(targetUrl);

      // 构建新的完整URL
      String newPath = currentUrl.replace(sourceUri.toString(), "");
      if (newPath.startsWith("/")) {
        newPath = newPath.substring(1);
      }

      template.target(targetUri.toString());
      if (StringUtils.isNotBlank(newPath)) {
        template.uri("/" + newPath);
      }
    }
  }

  /**
   * 替换服务名称
   */
  private void replaceServiceName(RequestTemplate template, String sourceServiceName,
      String targetServiceName) {
    // 如果目标是IP地址格式
    if (targetServiceName.startsWith("http://") || targetServiceName.startsWith("https://")) {
      template.target(targetServiceName);
    }
    // 如果目标也是服务名称
    else {
      // 通过Nacos获取目标服务的实例
      if (nacosDiscoveryClient != null) {
        try {
          List<ServiceInstance> instances = nacosDiscoveryClient.getInstances(targetServiceName);
          if (!CollectionUtils.isEmpty(instances)) {
            // 随机选择一个实例
            ServiceInstance selectedInstance = instances.get(random.nextInt(instances.size()));
            String targetUrl = selectedInstance.getUri().toString();
            template.target(targetUrl);
            log.debug("服务路由转发: {} -> {} ({})", sourceServiceName, targetServiceName,
                targetUrl);
          } else {
            log.warn("目标服务 {} 没有可用实例", targetServiceName);
          }
        } catch (Exception e) {
          log.error("获取目标服务实例失败: {}", e.getMessage(), e);
        }
      } else {
        log.warn("NacosDiscoveryClient为空，无法进行服务发现");
      }
    }
  }
}